import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { StatusCardComponent } from '../../shared/status-card/status-card.component';
import { TaskService } from '../../core/services/task.service';

function futureDateValidator(ctrl: AbstractControl): ValidationErrors | null {
    const v = ctrl.value as Date | null;
    if (!v) return null;
    const d = new Date(v.toDateString()).getTime();
    const today = new Date(new Date().toDateString()).getTime();
    return d < today ? { pastDate: true } : null;
}

@Component({
    standalone: true,
    selector: 'tm-home',
    imports: [
        CommonModule, ReactiveFormsModule,
        MatFormFieldModule, MatInputModule, MatSelectModule,
        MatDatepickerModule, MatNativeDateModule, MatButtonModule, MatIconModule,
        StatusCardComponent
    ],
    templateUrl: './home.component.html',
    styleUrls: ['./home.component.scss']
})
export class HomeComponent {
    private fb = inject(FormBuilder);
    private tasks = inject(TaskService);

    counts = this.tasks.counts;

    form = this.fb.group({
        title: ['', [Validators.required, Validators.maxLength(100)]],
        dueDate: [null as Date | null, [Validators.required, futureDateValidator]],
        description: ['', [Validators.maxLength(1000)]],
        priority: ['medium', [Validators.required]],
    });

    get f() { return this.form.controls; }

    createTask() {
        if (this.form.invalid) {
            this.form.markAllAsTouched();
            return;
        }
        const { title, description, dueDate, priority } = this.form.value;
        this.tasks.add({
            title: title!.trim(),
            description: description?.trim(),
            dueDate: (dueDate as Date).toISOString(),
            priority: priority as any,
        });
        this.form.reset({ priority: 'medium', dueDate: null });
    }
}
