import { Component, Input } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

@Component({
    standalone: true,
    selector: 'tm-status-card',
    imports: [MatCardModule, MatIconModule, MatButtonModule],
    template: `
    <mat-card class="status-card">
      <div class="label">{{ label }}</div>
      <div class="value">{{ value }}</div>
      <button mat-mini-fab class="ghost-btn" type="button" aria-label="quick add">
        <mat-icon>add</mat-icon>
      </button>
    </mat-card>
  `,
    styleUrls: ['./status-card.component.scss']
})
export class StatusCardComponent {
    @Input() label = '';
    @Input() value: number | string = 0;
}
