{"version": 3, "sources": ["optional-peer-dep:__vite-optional-peer-dep:@angular/animations/browser:@angular/platform-browser:false", "../../../../../../node_modules/@angular/platform-browser/fesm2022/animations.mjs"], "sourcesContent": ["module.exports = {};throw new Error(`Could not resolve \"@angular/animations/browser\" imported by \"@angular/platform-browser\". Is it installed?`)", "/**\n * @license Angular v20.2.1\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, ANIMATION_MODULE_TYPE, RendererFactory2, NgZone, NgModule, ɵperformanceMarkFeature as _performanceMarkFeature } from '@angular/core';\nexport { ANIMATION_MODULE_TYPE } from '@angular/core';\nimport * as i1 from '@angular/animations/browser';\nimport { ɵAnimationEngine as _AnimationEngine, AnimationDriver, NoopAnimationDriver, ɵWebAnimationsDriver as _WebAnimationsDriver, ɵAnimationStyleNormalizer as _AnimationStyleNormalizer, ɵWebAnimationsStyleNormalizer as _WebAnimationsStyleNormalizer, ɵAnimationRendererFactory as _AnimationRendererFactory } from '@angular/animations/browser';\nimport { DOCUMENT } from '@angular/common';\nimport { DomRendererFactory2 } from './dom_renderer.mjs';\nimport { BrowserModule } from './browser.mjs';\nclass InjectableAnimationEngine extends _AnimationEngine {\n  // The `ApplicationRef` is injected here explicitly to force the dependency ordering.\n  // Since the `ApplicationRef` should be created earlier before the `AnimationEngine`, they\n  // both have `ngOnDestroy` hooks and `flush()` must be called after all views are destroyed.\n  constructor(doc, driver, normalizer) {\n    super(doc, driver, normalizer);\n  }\n  ngOnDestroy() {\n    this.flush();\n  }\n  static ɵfac = function InjectableAnimationEngine_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InjectableAnimationEngine)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.AnimationDriver), i0.ɵɵinject(i1.ɵAnimationStyleNormalizer));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InjectableAnimationEngine,\n    factory: InjectableAnimationEngine.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InjectableAnimationEngine, [{\n    type: Injectable\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1.AnimationDriver\n  }, {\n    type: i1.ɵAnimationStyleNormalizer\n  }], null);\n})();\nfunction instantiateDefaultStyleNormalizer() {\n  return new _WebAnimationsStyleNormalizer();\n}\nfunction instantiateRendererFactory(renderer, engine, zone) {\n  return new _AnimationRendererFactory(renderer, engine, zone);\n}\nconst SHARED_ANIMATION_PROVIDERS = [{\n  provide: _AnimationStyleNormalizer,\n  useFactory: instantiateDefaultStyleNormalizer\n}, {\n  provide: _AnimationEngine,\n  useClass: InjectableAnimationEngine\n}, {\n  provide: RendererFactory2,\n  useFactory: instantiateRendererFactory,\n  deps: [DomRendererFactory2, _AnimationEngine, NgZone]\n}];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\nconst BROWSER_NOOP_ANIMATIONS_PROVIDERS = [{\n  provide: AnimationDriver,\n  useClass: NoopAnimationDriver\n}, {\n  provide: ANIMATION_MODULE_TYPE,\n  useValue: 'NoopAnimations'\n}, ...SHARED_ANIMATION_PROVIDERS];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\nconst BROWSER_ANIMATIONS_PROVIDERS = [\n// Note: the `ngServerMode` happen inside factories to give the variable time to initialize.\n{\n  provide: AnimationDriver,\n  useFactory: () => typeof ngServerMode !== 'undefined' && ngServerMode ? new NoopAnimationDriver() : new _WebAnimationsDriver()\n}, {\n  provide: ANIMATION_MODULE_TYPE,\n  useFactory: () => typeof ngServerMode !== 'undefined' && ngServerMode ? 'NoopAnimations' : 'BrowserAnimations'\n}, ...SHARED_ANIMATION_PROVIDERS];\n\n/**\n * Exports `BrowserModule` with additional dependency-injection providers\n * for use with animations. See [Animations](guide/animations).\n * @publicApi\n *\n * @deprecated 20.2 Use `animate.enter` or `animate.leave` instead. Intent to remove in v23\n */\nclass BrowserAnimationsModule {\n  /**\n   * Configures the module based on the specified object.\n   *\n   * @param config Object used to configure the behavior of the `BrowserAnimationsModule`.\n   * @see {@link BrowserAnimationsModuleConfig}\n   *\n   * @usageNotes\n   * When registering the `BrowserAnimationsModule`, you can use the `withConfig`\n   * function as follows:\n   * ```ts\n   * @NgModule({\n   *   imports: [BrowserAnimationsModule.withConfig(config)]\n   * })\n   * class MyNgModule {}\n   * ```\n   */\n  static withConfig(config) {\n    return {\n      ngModule: BrowserAnimationsModule,\n      providers: config.disableAnimations ? BROWSER_NOOP_ANIMATIONS_PROVIDERS : BROWSER_ANIMATIONS_PROVIDERS\n    };\n  }\n  static ɵfac = function BrowserAnimationsModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BrowserAnimationsModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BrowserAnimationsModule,\n    exports: [BrowserModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: BROWSER_ANIMATIONS_PROVIDERS,\n    imports: [BrowserModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserAnimationsModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: BROWSER_ANIMATIONS_PROVIDERS\n    }]\n  }], null, null);\n})();\n/**\n * Returns the set of dependency-injection providers\n * to enable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to enable animations in an application\n * bootstrapped using the `bootstrapApplication` function. In this scenario there\n * is no need to import the `BrowserAnimationsModule` NgModule at all, just add\n * providers returned by this function to the `providers` list as show below.\n *\n * ```ts\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n *\n * @deprecated 20.2 Use `animate.enter` or `animate.leave` instead. Intent to remove in v23\n *\n */\nfunction provideAnimations() {\n  _performanceMarkFeature('NgEagerAnimations');\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideAnimations` call results in app code.\n  return [...BROWSER_ANIMATIONS_PROVIDERS];\n}\n/**\n * A null player that must be imported to allow disabling of animations.\n * @publicApi\n *\n * @deprecated 20.2 Use `animate.enter` or `animate.leave` instead. Intent to remove in v23\n */\nclass NoopAnimationsModule {\n  static ɵfac = function NoopAnimationsModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NoopAnimationsModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NoopAnimationsModule,\n    exports: [BrowserModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n    imports: [BrowserModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoopAnimationsModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS\n    }]\n  }], null, null);\n})();\n/**\n * Returns the set of dependency-injection providers\n * to disable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to bootstrap an application using\n * the `bootstrapApplication` function, but you need to disable animations\n * (for example, when running tests).\n *\n * ```ts\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideNoopAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n *\n * @deprecated 20.2 Use `animate.enter` or `animate.leave` instead. Intent to remove in v23\n */\nfunction provideNoopAnimations() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideNoopAnimations` call results in app code.\n  return [...BROWSER_NOOP_ANIMATIONS_PROVIDERS];\n}\nexport { BrowserAnimationsModule, NoopAnimationsModule, provideAnimations, provideNoopAnimations, InjectableAnimationEngine as ɵInjectableAnimationEngine };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,WAAO,UAAU,CAAC;AAAE,UAAM,IAAI,MAAM,2GAA2G;AAAA;AAAA;;;ACS/I,SAAoB;AACpB,qBAAyT;AAIzT,IAAM,4BAAN,MAAM,mCAAkC,eAAAA,iBAAiB;AAAA;AAAA;AAAA;AAAA,EAIvD,YAAY,KAAK,QAAQ,YAAY;AACnC,UAAM,KAAK,QAAQ,UAAU;AAAA,EAC/B;AAAA,EACA,cAAc;AACZ,SAAK,MAAM;AAAA,EACb;AAAA,EACA,OAAO,OAAO,SAAS,kCAAkC,mBAAmB;AAC1E,WAAO,KAAK,qBAAqB,4BAA8B,SAAS,QAAQ,GAAM,SAAY,kBAAe,GAAM,SAAY,4BAAyB,CAAC;AAAA,EAC/J;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,2BAA0B;AAAA,EACrC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,oCAAoC;AAC3C,SAAO,IAAI,eAAAC,8BAA8B;AAC3C;AACA,SAAS,2BAA2B,UAAU,QAAQ,MAAM;AAC1D,SAAO,IAAI,eAAAC,0BAA0B,UAAU,QAAQ,IAAI;AAC7D;AACA,IAAM,6BAA6B,CAAC;AAAA,EAClC,SAAS,eAAAC;AAAA,EACT,YAAY;AACd,GAAG;AAAA,EACD,SAAS,eAAAH;AAAA,EACT,UAAU;AACZ,GAAG;AAAA,EACD,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,qBAAqB,eAAAA,kBAAkB,MAAM;AACtD,CAAC;AAKD,IAAM,oCAAoC,CAAC;AAAA,EACzC,SAAS;AAAA,EACT,UAAU;AACZ,GAAG;AAAA,EACD,SAAS;AAAA,EACT,UAAU;AACZ,GAAG,GAAG,0BAA0B;AAKhC,IAAM,+BAA+B;AAAA;AAAA,EAErC;AAAA,IACE,SAAS;AAAA,IACT,YAAY,MAA6C,QAAe,IAAI,mCAAoB,IAAI,IAAI,eAAAI,qBAAqB;AAAA,EAC/H;AAAA,EAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY,MAA6C,QAAe,mBAAmB;AAAA,EAC7F;AAAA,EAAG,GAAG;AAA0B;AAShC,IAAM,0BAAN,MAAM,yBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiB5B,OAAO,WAAW,QAAQ;AACxB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,OAAO,oBAAoB,oCAAoC;AAAA,IAC5E;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,aAAa;AAAA,EACzB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW;AAAA,IACX,SAAS,CAAC,aAAa;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,aAAa;AAAA,MACvB,WAAW;AAAA,IACb,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AA0BH,SAAS,oBAAoB;AAC3B,yBAAwB,mBAAmB;AAG3C,SAAO,CAAC,GAAG,4BAA4B;AACzC;AAOA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,aAAa;AAAA,EACzB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW;AAAA,IACX,SAAS,CAAC,aAAa;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,aAAa;AAAA,MACvB,WAAW;AAAA,IACb,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAwBH,SAAS,wBAAwB;AAG/B,SAAO,CAAC,GAAG,iCAAiC;AAC9C;", "names": ["_AnimationEngine", "_WebAnimationsStyleNormalizer", "_AnimationRendererFactory", "_AnimationStyleNormalizer", "_WebAnimationsDriver"]}