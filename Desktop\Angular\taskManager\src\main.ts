import { bootstrapApplication } from '@angular/platform-browser';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideHttpClient } from '@angular/common/http';
import { provideRouter } from '@angular/router';
import { routes } from './app/app.routes';
import { HomeComponent } from './app/features/home/<USER>';

bootstrapApplication(HomeComponent, {
  providers: [provideRouter(routes), provideAnimations(), provideHttpClient()]
}).catch(err => console.error(err));
