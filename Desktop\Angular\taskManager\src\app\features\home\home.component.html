<div class="home-wrap">
  <header class="topbar">
    <h1>Task Manager</h1>
    <!-- Dashboard comes in Milestone 2 -->
    <button mat-stroked-button color="primary" disabled>
      <mat-icon>dashboard</mat-icon>&nbsp;View Dashboard (soon)
    </button>
  </header>

  <section class="status-grid">
    <tm-status-card label="Tasks Today" [value]="counts().today"></tm-status-card>
    <tm-status-card label="In Progress" [value]="counts().inProgress"></tm-status-card>
    <tm-status-card label="Completed" [value]="counts().completed"></tm-status-card>
  </section>

  <section class="create-card">
    <h2>Create New Task</h2>

    <form [formGroup]="form" (ngSubmit)="createTask()" novalidate>
      <div class="grid">
        <mat-form-field appearance="outline">
          <mat-label>Task Title *</mat-label>
          <input matInput formControlName="title" placeholder="Enter task title" />
          <mat-error *ngIf="f.title.hasError('required')">Title is required.</mat-error>
          <mat-error *ngIf="f.title.hasError('maxlength')">Max 100 characters.</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Due Date *</mat-label>
          <input matInput [matDatepicker]="dp" formControlName="dueDate" placeholder="Pick a date" />
          <mat-datepicker-toggle matSuffix [for]="dp"></mat-datepicker-toggle>
          <mat-datepicker #dp></mat-datepicker>
          <mat-error *ngIf="f.dueDate.hasError('required')">Due date is required.</mat-error>
          <mat-error *ngIf="f.dueDate.hasError('pastDate')">Due date cannot be in the past.</mat-error>
        </mat-form-field>
      </div>

      <mat-form-field appearance="outline" class="full">
        <mat-label>Description</mat-label>
        <textarea matInput rows="4" formControlName="description" placeholder="Add task description..."></textarea>
        <mat-hint align="end">{{ f.description.value?.length || 0 }}/1000</mat-hint>
      </mat-form-field>

      <div class="grid">
        <mat-form-field appearance="outline">
          <mat-label>Priority</mat-label>
          <mat-select formControlName="priority">
            <mat-option value="low">Low</mat-option>
            <mat-option value="medium">Medium</mat-option>
            <mat-option value="high">High</mat-option>
          </mat-select>
        </mat-form-field>

        <div class="actions">
          <button mat-raised-button color="primary" type="submit">Create Task</button>
          <button mat-button type="button" (click)="form.reset({ priority: 'medium', dueDate: null })">Reset</button>
        </div>
      </div>
    </form>
  </section>
</div>
