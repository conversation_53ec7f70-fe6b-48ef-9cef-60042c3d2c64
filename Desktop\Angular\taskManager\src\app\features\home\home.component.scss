.home-wrap {
    padding: 24px;
    max-width: 1100px;
    margin: 0 auto;

    .topbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h1 {
            margin: 0;
            font-weight: 700;
        }
    }

    .status-grid {
        display: grid;
        grid-template-columns: repeat(3, minmax(0, 1fr));
        gap: 16px;
        margin-bottom: 22px;

        @media (max-width: 900px) {
            grid-template-columns: 1fr;
        }
    }

    .create-card {
        background: #fff;
        border-radius: 14px;
        padding: 20px;
        box-shadow: 0 2px 14px rgba(0, 0, 0, .06);

        h2 {
            margin: 0 0 14px;
            font-weight: 700;
        }

        form {
            .grid {
                display: grid;
                gap: 16px;
                grid-template-columns: repeat(2, minmax(0, 1fr));

                @media (max-width: 900px) {
                    grid-template-columns: 1fr;
                }
            }

            .full {
                width: 100%;
                margin: 16px 0;
            }

            .actions {
                display: flex;
                align-items: center;
                gap: 12px;
                padding-top: 8px;
                justify-content: flex-end;
            }
        }
    }
}