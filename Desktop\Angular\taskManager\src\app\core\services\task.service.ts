import { Injectable, computed, signal } from '@angular/core';
import { Task } from '../models/task.model';

const STORAGE_KEY = 'tm_tasks_v1';

@Injectable({ providedIn: 'root' })
export class TaskService {
    private readonly _tasks = signal<Task[]>(this.load());
    readonly tasks = computed(() => this._tasks());
    readonly counts = computed(() => {
        const t = this._tasks();
        const today = new Date().toDateString();
        return {
            today: t.filter(x => new Date(x.dueDate).toDateString() === today).length,
            inProgress: t.filter(x => x.status === 'in-progress').length,
            completed: t.filter(x => x.status === 'completed').length,
        };
    });

    add(task: Omit<Task, 'id' | 'createdAt' | 'updatedAt' | 'status'> & { status?: Task['status'] }): Task {
        const now = new Date().toISOString();
        const newTask: Task = {
            id: crypto.randomUUID(),
            createdAt: now,
            updatedAt: now,
            status: task.status ?? 'pending',
            ...task,
        };
        const list = [newTask, ...this._tasks()];
        this._tasks.set(list);
        this.save(list);
        return newTask;
    }

    private load(): Task[] {
        try { return JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]'); }
        catch { return []; }
    }
    private save(list: Task[]) {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(list));
    }
}
